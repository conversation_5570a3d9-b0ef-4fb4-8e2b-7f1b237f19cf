# 🛡️ Windows 11 System Manager - Complete Control Over Your System

**Created by:** <PERSON><PERSON>
**GitHub:** [https://github.com/zinzied](https://github.com/zinzied)
**Year:** 2025

This repository contains Python scripts to manage Windows 11 updates, privacy, bloatware, and system performance. A comprehensive toolkit for Windows 11 system optimization and control.

## Introduction: Why Use Windows 11 System Manager?

Windows 11 comes with many features that users may want to control or disable for various reasons. This tool provides comprehensive management capabilities for:

### 🎯 **System Management Features:**

1. **Update Control**
   - Disable/enable Windows automatic updates
   - Prevent problematic updates from installing
   - Maintain system stability and control

2. **Privacy Enhancement**
   - Disable telemetry and data collection
   - Control advertising ID and location tracking
   - Remove feedback notifications and suggestions

3. **Bloatware Removal**
   - Remove unnecessary Windows apps
   - Disable Xbox services and Cortana
   - Clean up Start Menu suggestions

4. **Performance Optimization**
   - Optimize visual effects for speed
   - Disable unnecessary services
   - Improve system responsiveness

5. **OneDrive Management**
   - Complete OneDrive disable/enable
   - Remove OneDrive integration
   - Clean registry entries

2. **Professional/Enterprise Environments**
   - Need for controlled update deployment schedules
   - Testing requirements before rolling out updates
   - Maintaining specific system configurations for production environments

3. **Limited Bandwidth/Data Constraints**
   - Metered internet connections with data caps
   - Slow internet connections where large updates disrupt work
   - Remote locations with unreliable internet access

4. **Gaming and Performance**
   - Preventing updates during critical gaming sessions or live streams
   - Avoiding performance impacts from background update processes
   - Maintaining optimal system performance for resource-intensive applications

5. **Legacy Software Dependencies**
   - Running older software that may break with newer Windows versions
   - Maintaining compatibility with specialized hardware or industrial equipment
   - Preserving specific system configurations required by legacy applications

6. **Development and Testing**
   - Creating stable development environments
   - Testing software on specific Windows versions
   - Preventing unexpected changes during development cycles

7. **System Control and Privacy**
   - Maintaining full control over what gets installed on the system
   - Avoiding unwanted feature changes or UI modifications
   - Preventing automatic installation of optional features or apps

### ⚖️ **Important Considerations:**

**Security Trade-offs**: Disabling updates means missing important security patches. Consider:
- Only disable updates temporarily when necessary
- Manually install critical security updates when possible
- Re-enable updates periodically to stay protected
- Use additional security measures (antivirus, firewall) when updates are disabled

**Best Practices**:
- Create system restore points before disabling updates
- Keep the restore script readily available
- Monitor Microsoft security bulletins manually
- Consider selective update installation rather than complete disabling

## Files Structure

```
Windows-11-System-Manager/
├── launcher.py                    # Main menu launcher
├── disable_windows_updates.py     # Windows Update disable script
├── restore_windows_updates.py     # Windows Update restore script
├── modules/
│   ├── onedrive_manager.py        # OneDrive management
│   ├── telemetry_manager.py       # Privacy & telemetry control
│   ├── bloatware_manager.py       # Bloatware removal
│   └── performance_manager.py     # Performance optimization
├── README.md                      # This documentation
└── requirements.txt               # Dependencies
```

## Features Overview

### 🔄 **Update Management**
- **Disable Updates**: Stop services, modify registry, disable tasks, block URLs
- **Restore Updates**: Re-enable all Windows Update functionality
- **Granular Control**: Choose specific components to disable/enable

### ☁️ **OneDrive Management**
- **Complete Disable**: Stop processes, disable services, modify registry
- **Startup Prevention**: Remove from startup locations
- **Registry Cleanup**: Clean OneDrive-related registry entries
- **File Explorer Integration**: Remove OneDrive from File Explorer

### 🔒 **Privacy & Telemetry Control**
- **Telemetry Disable**: Stop diagnostic data collection
- **Advertising ID**: Disable targeted advertising
- **Location Tracking**: Control location access
- **Activity History**: Disable timeline and activity tracking
- **Feedback**: Remove Windows feedback notifications

### 🗑️ **Bloatware Removal**
- **Windows Apps**: Remove unnecessary pre-installed apps
- **Xbox Services**: Disable Xbox-related services
- **Cortana**: Disable Cortana and web search
- **Widgets**: Remove Windows 11 widgets
- **Edge Integration**: Disable Microsoft Edge integration
- **Start Menu**: Clean suggestions and ads

### ⚡ **Performance Optimization**
- **Services**: Disable unnecessary background services
- **Visual Effects**: Optimize for performance over appearance
- **Power Settings**: Set high-performance power plan
- **Memory Management**: Optimize memory usage
- **Search Indexing**: Disable for better performance

## Requirements

- Windows 11
- Python 3.6 or higher
- Administrator privileges (recommended for full functionality)

## Usage

### Quick Start

1. **Download and Extract** all files to a folder
2. **Run as Administrator** (recommended for full functionality):
   ```cmd
   # Right-click Command Prompt -> "Run as administrator"
   python launcher.py
   ```
3. **Choose your option** from the main menu
4. **Follow the prompts** for each feature
5. **Restart when recommended** for changes to take effect

### Main Menu Options

```
🔄 UPDATE MANAGEMENT:
1. 🚫 DISABLE Windows Updates
2. 🔄 RESTORE Windows Updates

🛠️ SYSTEM MANAGEMENT:
3. ☁️  OneDrive Management
4. 🔒 Privacy & Telemetry Control
5. 🗑️  Bloatware Removal
6. ⚡ Performance Optimization

📖 HELP & INFO:
7. ℹ️  INFORMATION
8. ❌ EXIT
```

### Individual Module Usage

You can also run individual modules directly:

```cmd
# OneDrive management
python modules/onedrive_manager.py

# Privacy and telemetry control
python modules/telemetry_manager.py

# Bloatware removal
python modules/bloatware_manager.py

# Performance optimization
python modules/performance_manager.py
```

## Safety and Restore Features

### 🔄 **Comprehensive Restore**
The tool includes a comprehensive restore script (`comprehensive_restore.py`) that can:
- Restore all Windows Update functionality
- Re-enable OneDrive services and integration
- Restore telemetry and diagnostic services
- Reset performance optimizations
- Re-enable Xbox services
- Restore all registry modifications

### 🛡️ **Safety Features**
- **Confirmation prompts** for all major operations
- **Detailed logging** of all changes made
- **Granular control** - choose specific features to modify
- **Administrator detection** - warns if not running with proper privileges
- **Error handling** - graceful handling of permission issues

### ⚠️ **Important Warnings**
- **Always run as Administrator** for full functionality
- **Create a system restore point** before making changes
- **Some features may affect system functionality** - review carefully
- **Restart recommended** after making changes
- **Keep restore scripts safe** for future use

### Restore Script Actions:
1. **Service Restoration**:
   - Re-enables all Windows Update services
   - Sets startup type to "Automatic"
   - Starts the services

2. **Registry Cleanup**:
   - Removes policy registry keys that disable updates

3. **Task Restoration**:
   - Re-enables all Windows Update scheduled tasks

4. **Network Restoration**:
   - Removes URL blocks from hosts file

## Important Notes

⚠️ **Warnings**:
- These scripts modify system settings and should be used with caution
- Always run as Administrator for full functionality
- A system restart is recommended after running either script
- Keep the restore script safe in case you need to re-enable updates

🔒 **Security Considerations**:
- Disabling Windows Updates can leave your system vulnerable to security threats
- Only disable updates temporarily and for specific reasons
- Consider re-enabling updates regularly to receive security patches

📝 **Compatibility**:
- Designed for Windows 11
- May work on Windows 10 with minor modifications
- Some features require Administrator privileges

## Troubleshooting

### Common Issues:

1. **"Access Denied" errors**:
   - Run the script as Administrator
   - Some operations require elevated privileges

2. **Service start/stop failures**:
   - Some services may be protected by Windows
   - Try running the script multiple times
   - Restart and try again

3. **Registry access errors**:
   - Ensure you're running as Administrator
   - Some registry keys may be protected

4. **Hosts file modification fails**:
   - Check if antivirus is blocking hosts file changes
   - Ensure Administrator privileges

### Manual Verification:

After running the disable script, you can verify:
- Services: `services.msc` → Check Windows Update services are stopped/disabled
- Registry: `regedit` → Navigate to Windows Update policy keys
- Tasks: `taskschd.msc` → Check Windows Update tasks are disabled
- Hosts: Check `C:\Windows\System32\drivers\etc\hosts` for blocked URLs

## License

These scripts are provided as-is for educational and administrative purposes. Use at your own risk.

## Author

**Zied Boughdir**
GitHub: [https://github.com/zinzied](https://github.com/zinzied)
Year: 2025

## 🌟 New Features Added

This enhanced version includes:
- ☁️ **OneDrive Management** - Complete disable/enable functionality
- 🔒 **Privacy & Telemetry Control** - Comprehensive privacy enhancement
- 🗑️ **Bloatware Removal** - Remove unnecessary Windows apps and features
- ⚡ **Performance Optimization** - System speed and responsiveness improvements
- 🔄 **Comprehensive Restore** - Safely undo all modifications
- 🎯 **Modular Design** - Use individual features as needed
- 🛡️ **Enhanced Safety** - Better error handling and confirmation prompts

## Examples and Use Cases

### 🎯 **Common Scenarios**

**Privacy-focused setup:**
```
1. Run launcher.py as Administrator
2. Choose option 5 (Privacy & Telemetry Control)
3. Select "Disable All Telemetry & Enhance Privacy"
4. Choose option 4 (OneDrive Management)
5. Select "Disable OneDrive Completely"
```

**Performance optimization:**
```
1. Run launcher.py as Administrator
2. Choose option 7 (Performance Optimization)
3. Select "Optimize All Performance Settings"
4. Choose option 6 (Bloatware Removal)
5. Select "Remove All Bloatware"
```

## Disclaimer

**⚠️ IMPORTANT DISCLAIMER ⚠️**

This tool modifies critical Windows system settings and should be used with caution. The author is not responsible for any system damage, data loss, or security vulnerabilities that may result from using these scripts.

**Use at your own risk and always:**
- Create a system backup before use
- Understand what each script does
- Test in a non-production environment first
- Keep the restore script accessible

Modifying Windows Update settings can impact system security and stability. Always ensure you have proper backups and understand the implications before disabling system updates.
